<?php
// Définir l'encodage par défaut pour PHP
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

include 'top-bar-informatique.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once('lib/FPDF/fpdf.php');

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        // Forcer l'encodage UTF-8
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

// Classe FPDF étendue pour supporter l'UTF-8 avec améliorations visuelles
class FPDF_UTF8 extends FPDF
{
    // Tableau de conversion des caractères UTF-8 vers ISO-8859-1
    private $utf8_to_iso = array(
        'à' => 'à', 'á' => 'á', 'â' => 'â', 'ã' => 'ã', 'ä' => 'ä', 'å' => 'å',
        'è' => 'è', 'é' => 'é', 'ê' => 'ê', 'ë' => 'ë',
        'ì' => 'ì', 'í' => 'í', 'î' => 'î', 'ï' => 'ï',
        'ò' => 'ò', 'ó' => 'ó', 'ô' => 'ô', 'õ' => 'õ', 'ö' => 'ö',
        'ù' => 'ù', 'ú' => 'ú', 'û' => 'û', 'ü' => 'ü',
        'ç' => 'ç', 'ñ' => 'ñ',
        'À' => 'À', 'Á' => 'Á', 'Â' => 'Â', 'Ã' => 'Ã', 'Ä' => 'Ä', 'Å' => 'Å',
        'È' => 'È', 'É' => 'É', 'Ê' => 'Ê', 'Ë' => 'Ë',
        'Ì' => 'Ì', 'Í' => 'Í', 'Î' => 'Î', 'Ï' => 'Ï',
        'Ò' => 'Ò', 'Ó' => 'Ó', 'Ô' => 'Ô', 'Õ' => 'Õ', 'Ö' => 'Ö',
        'Ù' => 'Ù', 'Ú' => 'Ú', 'Û' => 'Û', 'Ü' => 'Ü',
        'Ç' => 'Ç', 'Ñ' => 'Ñ',
        '€' => '€', '£' => '£', '¥' => '¥', '§' => '§', '©' => '©', '®' => '®',
        '°' => '°', '±' => '±', '²' => '²', '³' => '³', 'µ' => 'µ', '¶' => '¶',
        '¼' => '¼', '½' => '½', '¾' => '¾'
    );

    // Couleurs optimisées pour impression couleur ET noir/blanc
    private $colors = array(
        'primary' => array(0, 70, 150),      // Bleu Schlüter (bon contraste N&B)
        'secondary' => array(80, 80, 80),    // Gris foncé (remplace orange pour N&B)
        'text_dark' => array(0, 0, 0),       // Noir pur (contraste maximal)
        'text_light' => array(100, 100, 100), // Gris moyen (lisible en N&B)
        'border' => array(150, 150, 150),    // Gris bordure (visible en N&B)
        'background' => array(245, 245, 245) // Gris très clair (économie encre)
    );

    // Couleurs alternatives pour impression couleur uniquement
    private $colorColors = array(
        'primary' => array(0, 70, 150),      // Bleu Schlüter
        'secondary' => array(230, 126, 34),  // Orange accent
        'text_dark' => array(44, 62, 80),    // Gris foncé
        'text_light' => array(127, 140, 141), // Gris clair
        'border' => array(189, 195, 199),    // Gris bordure
        'background' => array(236, 240, 241) // Gris fond
    );

    private function convertText($txt)
    {
        // Si le texte est vide, le retourner tel quel
        if (empty($txt)) {
            return $txt;
        }

        // Si le texte est en UTF-8, le convertir
        if (mb_check_encoding($txt, 'UTF-8')) {
            // Utiliser iconv si disponible (plus fiable pour FPDF)
            if (function_exists('iconv')) {
                $converted = iconv('UTF-8', 'ISO-8859-1//IGNORE', $txt);
                if ($converted !== false) {
                    return $converted;
                }
            }

            // Fallback: utiliser notre tableau de correspondance
            $converted = strtr($txt, $this->utf8_to_iso);

            // Si ça ne marche toujours pas, essayer mb_convert_encoding
            if ($converted === $txt) {
                $converted = mb_convert_encoding($txt, 'ISO-8859-1', 'UTF-8');
            }

            return $converted;
        }

        return $txt;
    }

    // Méthodes d'amélioration visuelle
    function setColorFromPalette($colorName)
    {
        if (isset($this->colors[$colorName])) {
            $color = $this->colors[$colorName];
            $this->SetTextColor($color[0], $color[1], $color[2]);
        }
    }

    function setFillColorFromPalette($colorName)
    {
        if (isset($this->colors[$colorName])) {
            $color = $this->colors[$colorName];
            $this->SetFillColor($color[0], $color[1], $color[2]);
        }
    }

    function setDrawColorFromPalette($colorName)
    {
        if (isset($this->colors[$colorName])) {
            $color = $this->colors[$colorName];
            $this->SetDrawColor($color[0], $color[1], $color[2]);
        }
    }

    function addSectionSeparator($y_position = null)
    {
        if ($y_position) $this->SetY($y_position);
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.5);
        $this->Line(20, $this->GetY(), 190, $this->GetY());
        $this->Ln(8);
    }

    function addSignatureBox($x, $y, $width, $height, $title)
    {
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.8);
        $this->Rect($x, $y, $width, $height);

        // Titre de la boîte
        $this->SetXY($x, $y - 8);
        $this->SetFont('Arial', 'B', 10);
        $this->setColorFromPalette('text_dark');
        $this->Cell($width, 6, $title, 0, 0, 'C');

        // Ligne pour signature
        $this->SetLineWidth(0.3);
        $this->Line($x + 10, $y + $height - 15, $x + $width - 10, $y + $height - 15);

        // Texte "Signature"
        $this->SetXY($x + 10, $y + $height - 12);
        $this->SetFont('Arial', 'I', 8);
        $this->setColorFromPalette('text_light');
        $this->Cell($width - 20, 4, 'Signature', 0, 0, 'C');
    }

    // Boîte de signature optimisée pour une page
    function addCompactSignatureBox($x, $y, $width, $height, $title)
    {
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.6);
        $this->Rect($x, $y, $width, $height);

        // Titre compact avec plus d'espacement
        $this->SetXY($x, $y - 8); // Augmenté de -6 à -8mm pour plus d'espace
        $this->SetFont('Arial', 'B', 9);
        $this->setColorFromPalette('text_dark');
        $this->Cell($width, 5, $title, 0, 0, 'C');

        // Ligne pour signature
        $this->SetLineWidth(0.2);
        $this->Line($x + 8, $y + $height - 8, $x + $width - 8, $y + $height - 8);

        // Texte "Signature" compact
        $this->SetXY($x + 8, $y + $height - 6);
        $this->SetFont('Arial', 'I', 7);
        $this->setColorFromPalette('text_light');
        $this->Cell($width - 16, 3, 'Signature', 0, 0, 'C');
    }

    function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')
    {
        $txt = $this->convertText($txt);
        parent::Cell($w, $h, $txt, $border, $ln, $align, $fill, $link);
    }

    function MultiCell($w, $h, $txt, $border=0, $align='J', $fill=false)
    {
        $txt = $this->convertText($txt);
        parent::MultiCell($w, $h, $txt, $border, $align, $fill);
    }

    function Write($h, $txt, $link='')
    {
        $txt = $this->convertText($txt);
        parent::Write($h, $txt, $link);
    }

    // Pied de page amélioré
    function Footer()
    {
        $this->SetY(-20);
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.3);
        $this->Line(20, $this->GetY(), 190, $this->GetY());

        $this->SetY(-15);
        $this->SetFont('Arial', 'I', 8);
        $this->setColorFromPalette('text_light');

        // Informations de l'entreprise à gauche
        $this->Cell(0, 5, 'Schluter Systems - Service Informatique', 0, 0, 'L');

        // Numéro de page à droite
        $this->Cell(0, 5, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'R');
    }
}

// Fonction améliorée pour ajouter le logo de l'entreprise
function addCompanyLogo($pdf) {
    // Chemins possibles pour le logo
    $logoPaths = [
        'img/schluter-systems-logo-png-transparent.png',
        'C:/laragon/www/projet/img/schluter-systems-logo-png-transparent.png',
        '../img/schluter-systems-logo-png-transparent.png'
    ];

    $logoAdded = false;
    foreach ($logoPaths as $path) {
        if (file_exists($path)) {
            // Positionnement optimisé du logo
            $pdf->Image($path, 150, 15, 35, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
            $logoAdded = true;
            break;
        }
    }

    // Si aucun logo trouvé, ajouter un placeholder textuel élégant
    if (!$logoAdded) {
        $pdf->SetXY(150, 15);
        $pdf->SetFont('Arial', 'B', 12);
        $pdf->setColorFromPalette('primary');
        $pdf->Cell(35, 8, 'SCHLUTER', 0, 1, 'C');
        $pdf->SetX(150);
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(35, 6, 'SYSTEMS', 0, 0, 'C');
    }
}

// Fonction pour créer un en-tête optimisé pour une page
function addOptimizedPDFHeader($pdf, $isFirstPage = true, $ultraCompact = false) {
    // Logo de l'entreprise (position optimisée)
    addCompanyLogo($pdf);

    if ($isFirstPage) {
        if ($ultraCompact) {
            // Version ultra-compacte
            $pdf->SetY(20);
            $pdf->SetFont('Arial', 'B', 20);
            $pdf->setColorFromPalette('primary');
            $pdf->Cell(0, 10, 'ACCUSE DE RECEPTION', 0, 1, 'L');

            $pdf->SetFont('Arial', '', 9);
            $pdf->setColorFromPalette('text_light');
            $pdf->Cell(0, 5, 'Service Informatique - Schluter Systems', 0, 1, 'L');

            $pdf->Ln(3);
            $pdf->setDrawColorFromPalette('primary');
            $pdf->SetLineWidth(0.8);
            $pdf->Line(18, $pdf->GetY(), 100, $pdf->GetY());
            $pdf->Ln(8);
        } else {
            // Version compacte standard
            $pdf->SetY(22);
            $pdf->SetFont('Arial', 'B', 24);
            $pdf->setColorFromPalette('primary');
            $pdf->Cell(0, 12, 'ACCUSE DE RECEPTION', 0, 1, 'L');

            $pdf->SetFont('Arial', '', 10);
            $pdf->setColorFromPalette('text_light');
            $pdf->Cell(0, 6, 'Service Informatique - Schluter Systems', 0, 1, 'L');

            $pdf->Ln(4);
            $pdf->setDrawColorFromPalette('primary');
            $pdf->SetLineWidth(1.0);
            $pdf->Line(22, $pdf->GetY(), 110, $pdf->GetY());
            $pdf->Ln(10);
        }
    } else {
        // En-tête simplifié pour les pages suivantes
        $pdf->SetY(22);
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->setColorFromPalette('primary');
        $pdf->Cell(0, 8, 'ACCUSE DE RECEPTION (suite)', 0, 1, 'L');
        $pdf->Ln(6);
    }
}

// Fonction pour nettoyer et convertir le texte
function cleanText($text) {
    $text = strip_tags($text);
    if (!mb_check_encoding($text, 'UTF-8')) {
        $text = mb_convert_encoding($text, 'UTF-8', 'auto');
    }
    return $text;
}

// Fonction pour calculer l'espace optimal et ajuster la mise en page
function calculateOptimalLayout($pdf, $contentLength) {
    // Constantes de mise en page pour A4
    $pageHeight = 297; // mm
    $topMargin = 20;   // Réduit de 25 à 20
    $bottomMargin = 30; // Espace pour pied de page
    $headerHeight = 35; // En-tête

    $availableHeight = $pageHeight - $topMargin - $bottomMargin - $headerHeight;

    // Ajustement dynamique selon la longueur du contenu
    if ($contentLength > 500) { // Contenu long
        return [
            'spacing_section' => 8,    // Espacement réduit entre sections
            'spacing_line' => 5,       // Espacement entre lignes
            'info_box_height' => 28,   // Hauteur boîte info augmentée (était 20)
            'signature_height' => 25,  // Hauteur signatures réduite
            'font_size_title' => 11,   // Taille police titres
            'font_size_text' => 9,     // Taille police texte
            'margins' => 20,           // Marges réduites
            'spacing_before_signatures' => 12, // Espacement avant signatures
            'spacing_after_signature_title' => 6 // Espacement après titre "SIGNATURES"
        ];
    } else { // Contenu normal
        return [
            'spacing_section' => 10,
            'spacing_line' => 6,
            'info_box_height' => 30,   // Hauteur boîte info augmentée (était 22)
            'signature_height' => 30,
            'font_size_title' => 12,
            'font_size_text' => 10,
            'margins' => 22,
            'spacing_before_signatures' => 15, // Espacement avant signatures
            'spacing_after_signature_title' => 8 // Espacement après titre "SIGNATURES"
        ];
    }
}

function generateOptimizedPDFSection($pdf, $ar, $isFirstSection = false) {
    // Calculer la longueur du contenu pour optimiser la mise en page
    $contentLength = strlen(cleanText($ar['Remis']) . cleanText($ar['Recu']));
    $layout = calculateOptimalLayout($pdf, $contentLength);

    // Position de départ pour l'encadré
    $startY = $pdf->GetY();

    // Dessiner l'encadré d'abord (position fixe)
    $pdf->setFillColorFromPalette('background');
    $pdf->setDrawColorFromPalette('border');
    $pdf->SetLineWidth(0.3);
    $pdf->Rect(25, $startY, 160, $layout['info_box_height'], 'DF');

    // Contenu de l'encadré avec positionnement précis
    $pdf->SetXY(25, $startY + 3); // Position dans l'encadré avec marge interne
    $pdf->SetFont('Arial', 'B', $layout['font_size_title']);
    $pdf->setColorFromPalette('primary');
    $pdf->Cell(0, 6, 'INFORMATIONS GENERALES', 0, 1, 'L');

    // Repositionner pour le contenu
    $pdf->SetX(25);
    $pdf->Ln(2);

    // Collaborateur et responsable sur la même ligne (optimisé)
    $pdf->SetX(25);
    $pdf->SetFont('Arial', 'B', $layout['font_size_text']);
    $pdf->setColorFromPalette('text_dark');
    $pdf->Cell(35, $layout['spacing_line'], 'Collaborateur :', 0, 0, 'L');
    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    $pdf->Cell(65, $layout['spacing_line'], cleanText($ar['Collaborateur']), 0, 0, 'L');

    $pdf->SetFont('Arial', 'B', $layout['font_size_text']);
    $pdf->Cell(25, $layout['spacing_line'], 'Responsable :', 0, 0, 'L');
    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    $pdf->Cell(0, $layout['spacing_line'], cleanText($ar['Responsable']), 0, 1, 'L');

    // Date
    $pdf->SetX(25);
    $pdf->SetFont('Arial', 'B', $layout['font_size_text']);
    $pdf->setColorFromPalette('text_dark');
    $pdf->Cell(35, $layout['spacing_line'], 'Date :', 0, 0, 'L');
    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    $pdf->Cell(0, $layout['spacing_line'], date('d/m/Y', strtotime($ar['Date'])), 0, 1, 'L');

    // S'assurer qu'on sort de l'encadré
    $pdf->SetY($startY + $layout['info_box_height'] + $layout['spacing_section']);

    // Section "Matériel remis" optimisée
    $pdf->SetFont('Arial', 'B', $layout['font_size_title']);
    $pdf->setColorFromPalette('secondary');
    $pdf->Cell(0, 6, 'MATERIEL REMIS', 0, 1, 'L');

    $pdf->setDrawColorFromPalette('secondary');
    $pdf->SetLineWidth(0.5);
    $pdf->Line(25, $pdf->GetY(), 65, $pdf->GetY());
    $pdf->Ln(4);

    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    $pdf->setColorFromPalette('text_dark');
    $pdf->MultiCell(0, $layout['spacing_line'], cleanText($ar['Remis']), 0, 'L');
    $pdf->Ln($layout['spacing_section']);

    // Section "Matériel reçu" optimisée
    $pdf->SetFont('Arial', 'B', $layout['font_size_title']);
    $pdf->setColorFromPalette('secondary');
    $pdf->Cell(0, 6, 'MATERIEL RECU', 0, 1, 'L');

    $pdf->setDrawColorFromPalette('secondary');
    $pdf->SetLineWidth(0.5);
    $pdf->Line(25, $pdf->GetY(), 60, $pdf->GetY());
    $pdf->Ln(4);

    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    $pdf->setColorFromPalette('text_dark');
    $pdf->MultiCell(0, $layout['spacing_line'], cleanText($ar['Recu']), 0, 'L');

    // Espacement supplémentaire avant les signatures (10-15mm)
    $pdf->Ln($layout['spacing_before_signatures']);

    // Section signatures compacte
    $pdf->SetFont('Arial', 'B', $layout['font_size_title']);
    $pdf->setColorFromPalette('primary');
    $pdf->Cell(0, 6, 'SIGNATURES', 0, 1, 'L');
    $pdf->Ln($layout['spacing_after_signature_title']); // Espacement paramétrable

    // Boîtes de signature compactes
    $signatureY = $pdf->GetY();
    $pdf->addCompactSignatureBox(25, $signatureY, 75, $layout['signature_height'], 'Responsable Service Info.');
    $pdf->addCompactSignatureBox(110, $signatureY, 75, $layout['signature_height'], 'Collaborateur');

    $pdf->SetY($signatureY + $layout['signature_height'] + 8);
}

function generateSinglePDF($ar) {
    // Calculer la longueur du contenu pour optimiser les marges
    $contentLength = strlen(cleanText($ar['Remis']) . cleanText($ar['Recu']));
    $layout = calculateOptimalLayout(null, $contentLength);

    $pdf = new FPDF_UTF8();
    $pdf->SetAutoPageBreak(false); // Désactiver pour contrôler manuellement
    $pdf->AliasNbPages();
    $pdf->AddPage();
    $pdf->SetMargins($layout['margins'], $layout['margins'], $layout['margins']);

    // En-tête optimisé pour une page
    addOptimizedPDFHeader($pdf, true);

    // Contenu du document optimisé
    generateOptimizedPDFSection($pdf, $ar, true);

    // Vérifier si on dépasse la page et ajuster si nécessaire
    $currentY = $pdf->GetY();
    $pageHeight = 297; // A4 height in mm
    $bottomMargin = 30;

    if ($currentY > ($pageHeight - $bottomMargin)) {
        // Si on dépasse, régénérer avec des paramètres plus compacts
        $pdf = new FPDF_UTF8();
        $pdf->SetAutoPageBreak(false);
        $pdf->AliasNbPages();
        $pdf->AddPage();
        $pdf->SetMargins(18, 18, 18); // Marges encore plus réduites

        addOptimizedPDFHeader($pdf, true, true); // Mode ultra-compact
        generateOptimizedPDFSection($pdf, $ar, true);
    }

    // Informations supplémentaires compactes en bas
    $remainingSpace = ($pageHeight - $bottomMargin) - $pdf->GetY();
    if ($remainingSpace > 15) {
        $pdf->Ln(5);
        $pdf->setDrawColorFromPalette('border');
        $pdf->SetLineWidth(0.2);
        $pdf->Line($layout['margins'], $pdf->GetY(), 210 - $layout['margins'], $pdf->GetY());
        $pdf->Ln(4);

        $pdf->SetFont('Arial', 'I', 8);
        $pdf->setColorFromPalette('text_light');
        $pdf->Cell(0, 4, 'Document généré le ' . date('d/m/Y à H:i') . ' - Schlüter-Systems', 0, 1, 'C');
    }

    $filename = 'accuse_reception_' . $ar['ID'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
    ob_clean();
    $pdf->Output('D', $filename);
}

function generatePDF($data) {
    $pdf = new FPDF_UTF8();
    $pdf->SetAutoPageBreak(false); // Contrôle manuel pour optimisation
    $pdf->AliasNbPages();
    $pdf->AddPage();
    $pdf->SetMargins(22, 22, 22);

    // En-tête de la première page
    addOptimizedPDFHeader($pdf, true);

    $isFirstPage = true;
    foreach ($data as $index => $ar) {
        if (!$isFirstPage) {
            $pdf->AddPage();
            addOptimizedPDFHeader($pdf, false);
        }

        generateOptimizedPDFSection($pdf, $ar, $isFirstPage);
        $isFirstPage = false;

        // Vérifier si on a assez d'espace pour le prochain accusé
        if ($index < count($data) - 1) {
            $currentY = $pdf->GetY();
            $pageHeight = 297;
            $bottomMargin = 30;

            // Si moins de 80mm d'espace restant, nouvelle page
            if ($currentY > ($pageHeight - $bottomMargin - 80)) {
                // Nouvelle page pour le prochain accusé
                continue; // Le prochain accusé créera une nouvelle page
            } else {
                // Ajouter une séparation si on reste sur la même page
                $pdf->addSectionSeparator();
                $pdf->Ln(8);
            }
        }
    }

    $filename = 'historique_accuses_reception_' . date('Y-m-d_H-i-s') . '.pdf';
    ob_clean();
    $pdf->Output('D', $filename);
}

// Gestion de la recherche
$searchTerm = '';
$searchResults = 0;
$whereClause = '';
$params = [];

if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
    $searchTerm = trim($_GET['search']);
    $whereClause = "WHERE (collaborateur LIKE :search1 OR responsable LIKE :search2)";
    $params = [
        ':search1' => '%' . $searchTerm . '%',
        ':search2' => '%' . $searchTerm . '%'
    ];
}

// Récupération des accusés de réception avec recherche
$conn = getDbConnection();
$sql = "SELECT id as ID, collaborateur as Collaborateur, responsable as Responsable,
        date_creation as Date, materiel_remis as Remis, materiel_recu as Recu
        FROM accuses_reception
        $whereClause
        ORDER BY date_creation DESC";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$arData = $stmt->fetchAll(PDO::FETCH_ASSOC);
$searchResults = count($arData);

// Gestion de l'exportation PDF de tous les résultats
if (isset($_GET['download_pdf'])) {
    if (!empty($arData)) {
        generatePDF($arData);
        exit();
    } else {
        // Redirection si aucune donnée à exporter
        header("Location: historique.php");
        exit();
    }
}

// Gestion de l'exportation PDF d'un seul accusé
if (isset($_GET['download_single_pdf'])) {
    $id = intval($_GET['download_single_pdf']);
    $ar = null;
    foreach ($arData as $item) {
        if ($item['ID'] == $id) {
            $ar = $item;
            break;
        }
    }

    if ($ar) {
        generateSinglePDF($ar);
        exit();
    } else {
        // Redirection si accusé non trouvé
        header("Location: historique.php");
        exit();
    }
}

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historique des Accusés de Réception</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-container {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .search-form {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .search-input:focus {
            outline: none;
            border-color: #f57c00;
            box-shadow: 0 0 5px rgba(245, 124, 0, 0.3);
        }
        .search-btn {
            background-color: #f57c00;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .search-btn:hover {
            background-color: #e67e22;
        }
        .reset-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .reset-btn:hover {
            background-color: #5a6268;
        }
        .search-results {
            margin-top: 15px;
            padding: 10px;
            background-color: #e8f4fd;
            border-left: 4px solid #005ea2;
            border-radius: 4px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        /* Styles pour les boutons PDF */
        .btn-primary {
            background-color: #007bff;
            color: white;
            padding: 8px 12px;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
        }
        .btn-success:hover {
            background-color: #218838;
            color: white;
            text-decoration: none;
        }

        .text-center {
            text-align: center;
        }

        .export-section {
            text-align: right;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Historique Complet des Accusés de Réception</h1>

    <!-- Section de Recherche -->
    <div class="search-container">
        <h3>🔍 Rechercher des Accusés de Réception</h3>
        <form method="GET" action="" class="search-form">
            <input
                type="text"
                name="search"
                class="search-input"
                placeholder="Rechercher par nom de collaborateur ou responsable..."
                value="<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>"
                autocomplete="off"
            >
            <button type="submit" class="search-btn">🔍 Rechercher</button>
            <?php if (!empty($searchTerm)): ?>
                <a href="historique.php" class="reset-btn">🔄 Réinitialiser</a>
            <?php endif; ?>
        </form>

        <?php if (isset($_GET['search'])): ?>
            <div class="search-results">
                <?php if (!empty($searchTerm)): ?>
                    <strong>Résultats pour "<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>" :</strong>
                    <?= $searchResults ?> accusé(s) de réception trouvé(s)
                <?php else: ?>
                    <strong>Affichage de tous les accusés de réception</strong> (<?= $searchResults ?> au total)
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Section d'exportation PDF -->
    <?php if (!empty($arData)): ?>
        <div class="export-section">
            <h4 style="margin-top: 0; color: #28a745; text-align: left;">📄 Exportation PDF</h4>
            <p style="text-align: left; margin-bottom: 15px; color: #666;">
                Exportez les accusés de réception affichés au format PDF professionnel
            </p>
            <a href="?download_pdf=1<?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>"
               class="btn-success"
               title="Exporter tous les résultats affichés en PDF">
                📥 Exporter tout en PDF (<?= count($arData) ?> accusé<?= count($arData) > 1 ? 's' : '' ?>)
            </a>
        </div>
    <?php endif; ?>

    <div class="table-container">
        <?php
        // Fonction pour mettre en évidence les termes recherchés
        function highlightSearchTerm($text, $searchTerm) {
            if (empty($searchTerm)) {
                return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
            }

            $escapedText = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
            $escapedSearchTerm = htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8');

            // Recherche insensible à la casse
            $pattern = '/(' . preg_quote($escapedSearchTerm, '/') . ')/i';
            return preg_replace($pattern, '<span class="highlight">$1</span>', $escapedText);
        }
        ?>

        <table>
            <thead>
                <tr>
                    <th>Collaborateur</th>
                    <th>Responsable</th>
                    <th>Date</th>
                    <th>Remis</th>
                    <th>Reçu</th>
                    <th>PDF</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($arData)): ?>
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                            <?php if (!empty($searchTerm)): ?>
                                🔍 Aucun résultat trouvé pour "<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>"
                                <br><small>Essayez avec un autre terme de recherche</small>
                            <?php else: ?>
                                📋 Aucun accusé de réception disponible
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($arData as $ar): ?>
                        <tr>
                            <td><?= highlightSearchTerm($ar['Collaborateur'], $searchTerm) ?></td>
                            <td><?= highlightSearchTerm($ar['Responsable'], $searchTerm) ?></td>
                            <td><?= htmlspecialchars($ar['Date'], ENT_QUOTES, 'UTF-8') ?></td>
                            <td>
                                <?php if (!empty($ar['Remis'])): ?>
                                    <?= htmlspecialchars($ar['Remis'], ENT_QUOTES, 'UTF-8') ?>
                                <?php else: ?>
                                    <span style="color: #6c757d; font-style: italic;">Non renseigné</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (!empty($ar['Recu'])): ?>
                                    <?= htmlspecialchars($ar['Recu'], ENT_QUOTES, 'UTF-8') ?>
                                <?php else: ?>
                                    <span style="color: #6c757d; font-style: italic;">Non renseigné</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <a href="?download_single_pdf=<?= htmlspecialchars($ar['ID'], ENT_QUOTES, 'UTF-8') ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>"
                                   class="btn-primary"
                                   title="Télécharger le PDF de cet accusé de réception">
                                    📥 PDF
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Section d'informations -->
    <?php if (!empty($arData)): ?>
        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #28a745;">
            <h4 style="margin-top: 0; color: #28a745;">📊 Informations</h4>
            <p style="margin-bottom: 5px;">
                <strong>Total affiché :</strong> <?= count($arData) ?> accusé(s) de réception
            </p>
            <?php if (!empty($searchTerm)): ?>
                <p style="margin-bottom: 5px;">
                    <strong>Terme recherché :</strong> "<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>"
                </p>
            <?php endif; ?>
            <p style="margin-bottom: 0;">
                <strong>Dernière mise à jour :</strong> <?= date('d/m/Y à H:i:s') ?>
            </p>
        </div>
    <?php endif; ?>

    <!-- Conseils d'utilisation -->
    <div style="margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 5px; border-left: 4px solid #005ea2;">
        <h4 style="margin-top: 0; color: #005ea2;">💡 Conseils d'utilisation</h4>
        <ul style="margin-bottom: 0; padding-left: 20px;">
            <li>Utilisez la barre de recherche pour trouver rapidement un collaborateur ou responsable</li>
            <li>La recherche fonctionne avec les noms partiels (ex: "Martin" trouvera "Jean Martin")</li>
            <li>Les caractères accentués sont pris en charge (é, è, à, ç, etc.)</li>
            <li>Cliquez sur "Réinitialiser" pour afficher tous les accusés de réception</li>
        </ul>
    </div>
</div>

<script>
// Amélioration de l'expérience utilisateur
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input');
    const searchForm = document.querySelector('.search-form');

    // Focus automatique sur le champ de recherche si vide
    if (searchInput && searchInput.value === '') {
        searchInput.focus();
    }

    // Soumission du formulaire avec Entrée
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchForm.submit();
            }
        });
    }

    // Animation de chargement simple
    if (searchForm) {
        searchForm.addEventListener('submit', function() {
            const submitBtn = document.querySelector('.search-btn');
            if (submitBtn) {
                submitBtn.innerHTML = '🔄 Recherche...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>

<?php include 'bottom_bar.php'; ?>
</body>
</html>
