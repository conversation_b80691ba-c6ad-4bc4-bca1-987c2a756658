<?php
include 'top-bar-informatique.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

// Gestion de la recherche
$searchTerm = '';
$searchResults = 0;
$whereClause = '';
$params = [];

if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
    $searchTerm = trim($_GET['search']);
    $whereClause = "WHERE (collaborateur LIKE :search1 OR responsable LIKE :search2)";
    $params = [
        ':search1' => '%' . $searchTerm . '%',
        ':search2' => '%' . $searchTerm . '%'
    ];
}

// Récupération des accusés de réception avec recherche
$conn = getDbConnection();
$sql = "SELECT id as ID, collaborateur as Collaborateur, responsable as Responsable,
        date_creation as Date, materiel_remis as Remis, materiel_recu as Recu
        FROM accuses_reception
        $whereClause
        ORDER BY date_creation DESC";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$arData = $stmt->fetchAll(PDO::FETCH_ASSOC);
$searchResults = count($arData);

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historique des Accusés de Réception</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-container {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .search-form {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .search-input:focus {
            outline: none;
            border-color: #f57c00;
            box-shadow: 0 0 5px rgba(245, 124, 0, 0.3);
        }
        .search-btn {
            background-color: #f57c00;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .search-btn:hover {
            background-color: #e67e22;
        }
        .reset-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .reset-btn:hover {
            background-color: #5a6268;
        }
        .search-results {
            margin-top: 15px;
            padding: 10px;
            background-color: #e8f4fd;
            border-left: 4px solid #005ea2;
            border-radius: 4px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Historique Complet des Accusés de Réception</h1>

    <!-- Section de Recherche -->
    <div class="search-container">
        <h3>🔍 Rechercher des Accusés de Réception</h3>
        <form method="GET" action="" class="search-form">
            <input
                type="text"
                name="search"
                class="search-input"
                placeholder="Rechercher par nom de collaborateur ou responsable..."
                value="<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>"
                autocomplete="off"
            >
            <button type="submit" class="search-btn">🔍 Rechercher</button>
            <?php if (!empty($searchTerm)): ?>
                <a href="historique.php" class="reset-btn">🔄 Réinitialiser</a>
            <?php endif; ?>
        </form>

        <?php if (isset($_GET['search'])): ?>
            <div class="search-results">
                <?php if (!empty($searchTerm)): ?>
                    <strong>Résultats pour "<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>" :</strong>
                    <?= $searchResults ?> accusé(s) de réception trouvé(s)
                <?php else: ?>
                    <strong>Affichage de tous les accusés de réception</strong> (<?= $searchResults ?> au total)
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="table-container">
        <?php
        // Fonction pour mettre en évidence les termes recherchés
        function highlightSearchTerm($text, $searchTerm) {
            if (empty($searchTerm)) {
                return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
            }

            $escapedText = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
            $escapedSearchTerm = htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8');

            // Recherche insensible à la casse
            $pattern = '/(' . preg_quote($escapedSearchTerm, '/') . ')/i';
            return preg_replace($pattern, '<span class="highlight">$1</span>', $escapedText);
        }
        ?>

        <table>
            <thead>
                <tr>
                    <th>Collaborateur</th>
                    <th>Responsable</th>
                    <th>Date</th>
                    <th>Remis</th>
                    <th>Reçu</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($arData)): ?>
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                            <?php if (!empty($searchTerm)): ?>
                                🔍 Aucun résultat trouvé pour "<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>"
                                <br><small>Essayez avec un autre terme de recherche</small>
                            <?php else: ?>
                                📋 Aucun accusé de réception disponible
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($arData as $ar): ?>
                        <tr>
                            <td><?= highlightSearchTerm($ar['Collaborateur'], $searchTerm) ?></td>
                            <td><?= highlightSearchTerm($ar['Responsable'], $searchTerm) ?></td>
                            <td><?= htmlspecialchars($ar['Date'], ENT_QUOTES, 'UTF-8') ?></td>
                            <td><?= htmlspecialchars($ar['Remis'], ENT_QUOTES, 'UTF-8') ?></td>
                            <td><?= htmlspecialchars($ar['Recu'], ENT_QUOTES, 'UTF-8') ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Section d'informations -->
    <?php if (!empty($arData)): ?>
        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #28a745;">
            <h4 style="margin-top: 0; color: #28a745;">📊 Informations</h4>
            <p style="margin-bottom: 5px;">
                <strong>Total affiché :</strong> <?= count($arData) ?> accusé(s) de réception
            </p>
            <?php if (!empty($searchTerm)): ?>
                <p style="margin-bottom: 5px;">
                    <strong>Terme recherché :</strong> "<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>"
                </p>
            <?php endif; ?>
            <p style="margin-bottom: 0;">
                <strong>Dernière mise à jour :</strong> <?= date('d/m/Y à H:i:s') ?>
            </p>
        </div>
    <?php endif; ?>

    <!-- Conseils d'utilisation -->
    <div style="margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 5px; border-left: 4px solid #005ea2;">
        <h4 style="margin-top: 0; color: #005ea2;">💡 Conseils d'utilisation</h4>
        <ul style="margin-bottom: 0; padding-left: 20px;">
            <li>Utilisez la barre de recherche pour trouver rapidement un collaborateur ou responsable</li>
            <li>La recherche fonctionne avec les noms partiels (ex: "Martin" trouvera "Jean Martin")</li>
            <li>Les caractères accentués sont pris en charge (é, è, à, ç, etc.)</li>
            <li>Cliquez sur "Réinitialiser" pour afficher tous les accusés de réception</li>
        </ul>
    </div>
</div>

<script>
// Amélioration de l'expérience utilisateur
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input');
    const searchForm = document.querySelector('.search-form');

    // Focus automatique sur le champ de recherche si vide
    if (searchInput && searchInput.value === '') {
        searchInput.focus();
    }

    // Soumission du formulaire avec Entrée
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchForm.submit();
            }
        });
    }

    // Animation de chargement simple
    if (searchForm) {
        searchForm.addEventListener('submit', function() {
            const submitBtn = document.querySelector('.search-btn');
            if (submitBtn) {
                submitBtn.innerHTML = '🔄 Recherche...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>

<?php include 'bottom_bar.php'; ?>
</body>
</html>
