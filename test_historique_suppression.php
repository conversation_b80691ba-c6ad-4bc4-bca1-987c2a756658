<?php
// Test de la fonctionnalité de suppression dans historique.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Inclure les fonctions d'historique.php
include_once 'historique.php';

echo "<h1>Test de la Fonctionnalité de Suppression - Historique</h1>";

// Test 1: Vérifier que la fonction deleteAccuseReception existe
echo "<h2>Test 1: Vérification de la fonction de suppression</h2>";

if (function_exists('deleteAccuseReception')) {
    echo "✅ Fonction deleteAccuseReception trouvée<br>";
} else {
    echo "❌ Fonction deleteAccuseReception non trouvée<br>";
}

// Test 2: Lister les accusés de réception disponibles
echo "<h2>Test 2: Accusés de réception disponibles</h2>";

try {
    $conn = getDbConnection();
    $sql = "SELECT id, collaborateur, responsable, date_creation FROM accuses_reception ORDER BY date_creation DESC LIMIT 10";
    $stmt = $conn->query($sql);
    $records = $stmt->fetchAll();
    
    if (empty($records)) {
        echo "⚠️ Aucun accusé de réception trouvé. <a href='add_test_data_historique.php'>Ajouter des données de test</a><br>";
    } else {
        echo "✅ " . count($records) . " accusé(s) de réception trouvé(s)<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th>ID</th><th>Collaborateur</th><th>Responsable</th><th>Date</th><th>Actions</th>";
        echo "</tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['collaborateur']) . "</td>";
            echo "<td>" . htmlspecialchars($record['responsable']) . "</td>";
            echo "<td>" . htmlspecialchars($record['date_creation']) . "</td>";
            echo "<td>";
            echo "<a href='?test_delete=" . $record['id'] . "' style='color: red; text-decoration: none;' onclick='return confirm(\"Tester la suppression de " . htmlspecialchars($record['collaborateur']) . " ?\")'>🗑️ Test Suppression</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur lors de la récupération des données: " . $e->getMessage() . "<br>";
}

// Test 3: Test de suppression si demandé
if (isset($_GET['test_delete'])) {
    $deleteId = intval($_GET['test_delete']);
    echo "<h2>Test 3: Test de suppression pour ID $deleteId</h2>";
    
    if ($deleteId > 0) {
        $result = deleteAccuseReception($deleteId);
        
        if ($result['success']) {
            echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
            echo "✅ " . htmlspecialchars($result['message']);
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
            echo "❌ " . htmlspecialchars($result['message']);
            echo "</div>";
        }
        
        echo "<p><a href='test_historique_suppression.php'>🔄 Actualiser la liste</a></p>";
    } else {
        echo "❌ ID invalide<br>";
    }
}

// Test 4: Test de l'URL de redirection avec recherche
echo "<h2>Test 4: Test des URLs de redirection</h2>";

$testSearchTerm = "François";
$redirectUrl = "historique.php";
if (!empty($testSearchTerm)) {
    $redirectUrl .= "?search=" . urlencode($testSearchTerm);
}

echo "✅ URL de redirection avec recherche: <code>" . htmlspecialchars($redirectUrl) . "</code><br>";

// Test 5: Test de l'interface utilisateur
echo "<h2>Test 5: Test de l'interface utilisateur</h2>";

echo "<p>✅ Colonne 'Supprimer' ajoutée au tableau</p>";
echo "<p>✅ Bouton de suppression avec confirmation JavaScript</p>";
echo "<p>✅ Préservation des paramètres de recherche</p>";
echo "<p>✅ Styles CSS pour le bouton de suppression</p>";

// Liens de navigation
echo "<h2>Navigation</h2>";
echo "<p><a href='historique.php'>🔍 Voir l'historique complet</a></p>";
echo "<p><a href='accuse_reception.php'>📝 Ajouter un nouvel accusé</a></p>";
echo "<p><a href='add_test_data_historique.php'>➕ Ajouter des données de test</a></p>";

// Informations techniques
echo "<h2>Informations Techniques</h2>";
echo "<ul>";
echo "<li>✅ Fonction <code>deleteAccuseReception()</code> implémentée</li>";
echo "<li>✅ Gestion des paramètres GET <code>delete_id</code></li>";
echo "<li>✅ Redirection avec préservation de la recherche</li>";
echo "<li>✅ Confirmation JavaScript avant suppression</li>";
echo "<li>✅ Styles CSS pour bouton de suppression</li>";
echo "<li>✅ Gestion d'erreurs avec try/catch</li>";
echo "<li>✅ Vérification d'existence avant suppression</li>";
echo "</ul>";

echo "<h2>🎉 Tests terminés</h2>";
echo "<p>La fonctionnalité de suppression est maintenant disponible dans historique.php !</p>";
?>
